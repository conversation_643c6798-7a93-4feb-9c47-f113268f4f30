# Book Market Backend

A Node.js backend API for a book marketplace application built with Express.js and MongoDB.

## Features

- User authentication and authorization
- Book management (CRUD operations)
- Image upload with Cloudinary integration
- JWT-based authentication
- Password hashing with bcrypt
- CORS enabled for cross-origin requests
- Scheduled tasks with cron jobs

## Tech Stack

- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM for MongoDB
- **JWT** - Authentication tokens
- **bcryptjs** - Password hashing
- **Cloudinary** - Image storage and management
- **CORS** - Cross-origin resource sharing
- **dotenv** - Environment variable management
- **nodemon** - Development server

## Project Structure

```
src/
├── index.js              # Main application entry point
├── lib/
│   ├── cloudinary.js     # Cloudinary configuration
│   ├── cron.js           # Scheduled tasks
│   └── db.js             # Database connection
├── middleware/
│   └── auth.middleware.js # Authentication middleware
├── models/
│   ├── Book.js           # Book model
│   └── User.js           # User model
└── routes/
    ├── authRoutes.js     # Authentication routes
    └── bookRoutes.js     # Book management routes
```

## Installation

1. Clone the repository:
```bash
git clone https://github.com/SameerHRV/RN-BookMarketBackend.git
cd RN-BookMarketBackend
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory and add your environment variables:
```env
PORT=3000
MONGODB_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
```

4. Start the development server:
```bash
npm run dev
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user

### Books
- `GET /api/books` - Get all books
- `POST /api/books` - Create a new book (authenticated)
- `PUT /api/books/:id` - Update a book (authenticated)
- `DELETE /api/books/:id` - Delete a book (authenticated)

## Environment Variables

Make sure to set up the following environment variables:

- `PORT` - Server port (default: 3000)
- `MONGODB_URI` - MongoDB connection string
- `JWT_SECRET` - Secret key for JWT tokens
- `CLOUDINARY_CLOUD_NAME` - Cloudinary cloud name
- `CLOUDINARY_API_KEY` - Cloudinary API key
- `CLOUDINARY_API_SECRET` - Cloudinary API secret

## License

ISC
