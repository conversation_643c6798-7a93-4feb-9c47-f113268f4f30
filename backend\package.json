{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js"}, "keywords": [], "author": "", "type": "module", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^2.6.0", "cors": "^2.8.5", "cron": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.12.1"}, "devDependencies": {"nodemon": "^3.1.9"}}