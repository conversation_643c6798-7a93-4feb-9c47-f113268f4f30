{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../src/utils.ts"], "names": [], "mappings": "AAAA,UAAU;AACV,OAAO,EAAiC,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAEtF,MAAM,UAAU,eAAe,CAC7B,UAAsD;IAEtD,MAAM,2BAA2B,GAA0C;QACzE,MAAM,EAAE,CAAC,QAAQ,CAAC;QAClB,MAAM,EAAE,CAAC,QAAQ,CAAC;QAClB,GAAG,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;KAC1B,CAAC;IAEF,IACE,UAAU,KAAK,gBAAgB,CAAC,MAAM;QACtC,UAAU,KAAK,gBAAgB,CAAC,MAAM;QACtC,UAAU,KAAK,gBAAgB,CAAC,GAAG,EACnC,CAAC;QACD,OAAO,CAAC,IAAI,CACV,sJAAsJ,CACvJ,CAAC;QACF,OAAO,2BAA2B,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IACD,+CAA+C;IAC/C,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;QACnC,OAAO,CAAC,UAAU,CAAC,CAAC;IACtB,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,sFAAsF;AACtF,MAAM,UAAU,oBAAoB,CAAC,OAA2B;IAC9D,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QACxB,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,OAAO,EAAE,GAAG,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC;AAC/E,CAAC", "sourcesContent": ["// @hidden\nimport { ImagePickerOptions, MediaType, MediaTypeOptions } from './ImagePicker.types';\n\nexport function parseMediaTypes(\n  mediaTypes: MediaTypeOptions | MediaType | MediaType[]\n): MediaType[] {\n  const mediaTypeOptionsToMediaType: Record<MediaTypeOptions, MediaType[]> = {\n    Images: ['images'],\n    Videos: ['videos'],\n    All: ['images', 'videos'],\n  };\n\n  if (\n    mediaTypes === MediaTypeOptions.Images ||\n    mediaTypes === MediaTypeOptions.Videos ||\n    mediaTypes === MediaTypeOptions.All\n  ) {\n    console.warn(\n      '[expo-image-picker] `ImagePicker.MediaTypeOptions` have been deprecated. Use `ImagePicker.MediaType` or an array of `ImagePicker.MediaType` instead.'\n    );\n    return mediaTypeOptionsToMediaType[mediaTypes];\n  }\n  // Unlike iOS, Android can't auto-cast to array\n  if (typeof mediaTypes === 'string') {\n    return [mediaTypes];\n  }\n  return mediaTypes;\n}\n\n// We deprecated the MediaTypeOptions in SDK52, we should remove it in future release.\nexport function mapDeprecatedOptions(options: ImagePickerOptions) {\n  if (!options.mediaTypes) {\n    return options;\n  }\n  return { ...options, mediaTypes: parseMediaTypes(options.mediaTypes ?? []) };\n}\n"]}