import { Stack, useRouter, useSegments } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { SafeAreaProvider } from "react-native-safe-area-context";
import SafeScreen from "../components/SafeScreen";
import { useAuthStore } from "../store/authStore";
import { useEffect, useState } from "react";

export default function RootLayout() {
  const router = useRouter();
  const segments = useSegments();
  const [isNavigationReady, setIsNavigationReady] = useState(false);

  const { checkAuth, user, token, isCheckingAuth } = useAuthStore();

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    // Wait for auth check to complete before allowing navigation
    if (!isCheckingAuth && !isNavigationReady) {
      // Add a small delay to ensure the navigation system is ready
      const timer = setTimeout(() => {
        setIsNavigationReady(true);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isCheckingAuth, isNavigationReady]);

  useEffect(() => {
    if (!isNavigationReady) return;

    const isAuthScreen = segments[0] === "(auth)";
    const isSignedIn = user && token;

    try {
      if (!isAuthScreen && !isSignedIn) {
        router.replace("/(auth)");
      } else if (isAuthScreen && isSignedIn) {
        router.replace("/(tabs)");
      }
    } catch (error) {
      console.log("Navigation error:", error);
    }
  }, [user, token, segments, isNavigationReady, router]);

  return (
    <SafeAreaProvider>
      <SafeScreen>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="(tabs)" />
          <Stack.Screen name="(auth)" />
        </Stack>
      </SafeScreen>
      <StatusBar style="dark" />
    </SafeAreaProvider>
  );
}
